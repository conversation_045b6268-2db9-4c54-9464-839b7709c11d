import re
import os

def extract_titles(input_file, output_file):
    """
    Extract machine titles from the active listings file.
    Skips the defined sections at the top and extracts only the machine model names.
    """
    with open(input_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Skip the defined sections at the top
    # Find where the actual listings begin (after the comment sections)
    sections_pattern = r'# Defined replacements.*?\n\s*\n'
    match = re.search(sections_pattern, content, re.DOTALL)
    if match:
        # Start processing after the defined sections
        content = content[match.end():]
    
    # Split content into potential entries based on blank lines
    entries = re.split(r'\n\s*\n+', content)
    
    titles = []
    for entry in entries:
        lines = entry.strip().split('\n')
        if not lines or not lines[0].strip():
            continue
            
        # Get the first line as the title
        title = lines[0].strip()
        
        # Check if this looks like a machine listing
        # Should have multiple lines and the second line should be a year or ID
        if len(lines) >= 3:  # At least title, year/ID, and one more field
            second_line = lines[1].strip()
            # Check if second line is a 4-digit year or 7-8 digit ID
            if re.match(r'^\d{4}$', second_line) or re.match(r'^\d{7,8}$', second_line):
                # Remove year prefix from title if present
                title_without_year = re.sub(r'^\d{4}\s+', '', title)
                if title_without_year:  # Make sure we have something left after removing year
                    titles.append(title_without_year)
    
    # Write titles to output file
    with open(output_file, 'w', encoding='utf-8') as f:
        for title in titles:
            f.write(title + '\n')
    
    return len(titles)

def main():
    # File paths
    input_file = 'active listings.txt'
    output_file = 'extracted_titles.txt'
    
    try:
        count = extract_titles(input_file, output_file)
        print(f"Successfully extracted {count} titles to {output_file}")
        
        # Show first few titles as preview
        with open(output_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            print(f"\nFirst 10 titles:")
            for i, line in enumerate(lines[:10]):
                print(f"{i+1:2d}. {line.strip()}")
                
    except FileNotFoundError:
        print(f"Error: Could not find {input_file}")
    except Exception as e:
        print(f"Error: {e}")

if __name__ == "__main__":
    main()
